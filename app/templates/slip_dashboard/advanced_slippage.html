{% extends "base.html" %}

{% block head %}
<link rel="stylesheet" href="{{ url_for('static', filename='css/slip_home.css') }}"/>
<link rel="stylesheet" href="{{ url_for('static', filename='css/advanced_slippage.css') }}"/>
<link href='https://fonts.googleapis.com/css?family=Expletus Sans' rel='stylesheet'/>
<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.1/dist/css/bootstrap.min.css"/>
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/4.7.0/css/font-awesome.min.css"/>
<script src='https://cdn.plot.ly/plotly-latest.min.js'></script>
<style>
/* Enhanced SAMBA Theme Colors */
:root {
    --primary-color: #4d0000;
    --secondary-color: #c50000;
    --accent-blue: #0066cc;
    --accent-green: #28a745;
    --accent-orange: #fd7e14;
    --positive-color: #28a745;
    --negative-color: #dc3545;
    --neutral-color: #6c757d;
    --background-light: #f8f9fa;
    --background-white: #ffffff;
    --text-dark: #2f4251;
    --text-muted: #6c757d;
    --border-light: #e9ecef;
    --shadow-light: 0 2px 4px rgba(0,0,0,0.1);
    --shadow-medium: 0 4px 6px rgba(0,0,0,0.1);
    --gradient-primary: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    --gradient-light: linear-gradient(135deg, #f8f9fa, #e9ecef);
}

body {
    font-family: 'Expletus Sans', sans-serif;
    background-color: var(--background-light);
    line-height: 1.5;
}

.page-header {
    background: var(--gradient-primary);
    color: white;
    padding: 15px 20px;
    border-radius: 10px;
    margin-bottom: 20px;
    box-shadow: var(--shadow-medium);
    position: relative;
}

.page-header h1 {
    margin: 0;
    font-size: 24px;
    font-weight: 700;
}

.page-header p {
    margin: 5px 0 0 0;
    opacity: 0.9;
    font-size: 14px;
}

.refresh-info {
    position: absolute;
    top: 15px;
    right: 20px;
    font-size: 12px;
    opacity: 0.8;
}

/* Dashboard Summary */
.dashboard-summary {
    background: var(--background-white);
    border-radius: 10px;
    padding: 20px;
    margin-bottom: 20px;
    box-shadow: var(--shadow-light);
}

.summary-metrics {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
    gap: 15px;
    margin-bottom: 20px;
}

.summary-metric {
    background: var(--gradient-light);
    padding: 15px;
    border-radius: 8px;
    text-align: center;
    border-left: 4px solid var(--primary-color);
}

.summary-metric.positive { border-left-color: var(--positive-color); }
.summary-metric.negative { border-left-color: var(--negative-color); }
.summary-metric.neutral { border-left-color: var(--neutral-color); }

.metric-value {
    font-size: 20px;
    font-weight: 700;
    margin-bottom: 5px;
}

.metric-value.positive { color: var(--positive-color); }
.metric-value.negative { color: var(--negative-color); }
.metric-value.neutral { color: var(--primary-color); }

.metric-label {
    font-size: 11px;
    color: var(--text-muted);
    text-transform: uppercase;
    font-weight: 500;
}

.metric-change {
    font-size: 10px;
    margin-top: 3px;
}

/* Tab Navigation */
.nav-tabs {
    border-bottom: 2px solid var(--primary-color);
    margin-bottom: 20px;
}

.nav-tabs .nav-link {
    color: var(--text-dark);
    border: none;
    padding: 10px 20px;
    font-weight: 600;
    border-radius: 8px 8px 0 0;
    margin-right: 5px;
    font-size: 14px;
}

.nav-tabs .nav-link.active {
    background-color: var(--primary-color);
    color: white;
    border-bottom: 2px solid var(--primary-color);
}

.nav-tabs .nav-link:hover {
    background-color: rgba(77, 0, 0, 0.1);
}

/* Filter Panel */
.filter-panel {
    background: var(--background-white);
    padding: 15px 20px;
    border-radius: 10px;
    box-shadow: var(--shadow-light);
    margin-bottom: 20px;
    border: 1px solid var(--border-light);
}

.filter-panel h5 {
    color: var(--primary-color);
    margin-bottom: 15px;
    font-weight: 600;
    font-size: 16px;
}

.filter-row {
    display: flex;
    gap: 15px;
    align-items: end;
    flex-wrap: wrap;
    margin-bottom: 15px;
}

.filter-group {
    flex: 1;
    min-width: 150px;
}

.filter-group.date-range { min-width: 200px; }
.filter-group.slave-group { min-width: 180px; max-width: 200px; }
.filter-group.actions { flex: 0 0 auto; min-width: 160px; }

.form-control, .form-select {
    border: 1px solid var(--border-light);
    border-radius: 6px;
    padding: 6px 10px;
    font-size: 13px;
    height: 36px;
}

.form-control:focus, .form-select:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(77, 0, 0, 0.15);
}

.form-label {
    font-size: 12px;
    font-weight: 600;
    color: var(--text-dark);
    margin-bottom: 5px;
}

/* Button Styles */
.btn-primary {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
    padding: 8px 16px;
    font-weight: 600;
    border-radius: 6px;
    font-size: 13px;
    height: 36px;
}

.btn-primary:hover {
    background-color: var(--secondary-color);
    border-color: var(--secondary-color);
}

.btn-secondary {
    background-color: var(--neutral-color);
    border-color: var(--neutral-color);
    padding: 6px 12px;
    font-weight: 500;
    border-radius: 6px;
    font-size: 13px;
    height: 36px;
}

/* Quick Date Buttons */
.quick-dates {
    display: flex;
    gap: 8px;
    margin-top: 10px;
    flex-wrap: wrap;
}

.quick-date-btn {
    padding: 4px 8px;
    font-size: 11px;
    border: 1px solid var(--border-light);
    background: white;
    border-radius: 4px;
    cursor: pointer;
    color: var(--text-muted);
}

.quick-date-btn:hover {
    background-color: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

/* Content Cards */
.content-card {
    background: var(--background-white);
    border-radius: 10px;
    padding: 15px 20px;
    margin-bottom: 20px;
    box-shadow: var(--shadow-light);
    border: 1px solid var(--border-light);
}

.content-card h4 {
    color: var(--primary-color);
    margin-bottom: 15px;
    font-weight: 600;
    font-size: 18px;
    border-bottom: 2px solid var(--primary-color);
    padding-bottom: 8px;
}

/* Chart Containers */
.chart-container {
    background: var(--background-white);
    border-radius: 8px;
    padding: 10px;
    margin-bottom: 15px;
    min-height: 400px;
    border: 1px solid var(--border-light);
    position: relative;
    width: 100%;
    overflow: hidden;
    display: flex;
    flex-direction: column;
}

.chart-container.compact { min-height: 320px; }
.chart-container.large { min-height: 500px; }

.chart-container > div[id$="-chart"] {
    width: 100% !important;
    flex: 1;
    min-height: 350px;
    max-width: 100%;
    overflow: visible;
    display: block;
}

.chart-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 10px;
    flex-wrap: wrap;
    gap: 10px;
    flex-shrink: 0;
    padding: 5px 0;
}

.chart-title {
    font-size: 15px;
    font-weight: 600;
    color: var(--text-dark);
    margin: 0;
    flex: 1;
}

/* Loading States */
.loading {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 200px;
    color: var(--text-muted);
    flex-direction: column;
    gap: 10px;
}

.spinner {
    border: 3px solid var(--border-light);
    border-top: 3px solid var(--primary-color);
    border-radius: 50%;
    width: 24px;
    height: 24px;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Status Indicators */
.status-indicator {
    display: inline-flex;
    align-items: center;
    gap: 5px;
    font-size: 11px;
    padding: 3px 6px;
    border-radius: 12px;
    font-weight: 500;
}

.status-indicator.success {
    background-color: rgba(40, 167, 69, 0.1);
    color: var(--positive-color);
}

.status-indicator.info {
    background-color: rgba(0, 102, 204, 0.1);
    color: var(--accent-blue);
}

.status-dot {
    width: 6px;
    height: 6px;
    border-radius: 50%;
    background-color: currentColor;
}

/* Responsive Design */
@media (max-width: 768px) {
    .summary-metrics {
        grid-template-columns: repeat(2, 1fr);
    }

    .filter-row {
        flex-direction: column;
        gap: 10px;
    }

    .filter-group {
        min-width: auto;
    }

    .chart-container {
        min-height: 320px;
        padding: 8px;
    }
}

@media (max-width: 480px) {
    .summary-metrics {
        grid-template-columns: 1fr;
    }

    .chart-container {
        min-height: 250px;
    }
}
</style>
{% endblock head %}

{% block content %}
<main>
    <div class="container-fluid">
        <!-- Page Header -->
        <div class="page-header">
            <h1><i class="fa fa-line-chart"></i> Advanced Slippage Analysis</h1>
            <p>Comprehensive slippage monitoring and trade comparison dashboard</p>
            <div class="refresh-info">
                <div class="status-indicator info">
                    <span class="status-dot"></span>
                    Last updated: <span id="last-update-time">Loading...</span>
                </div>
            </div>
        </div>

        <!-- Dashboard Summary -->
        <div class="dashboard-summary">
            <div class="summary-metrics" id="summary-metrics">
                <div class="summary-metric positive">
                    <div class="metric-value positive" id="total-slippage">Loading...</div>
                    <div class="metric-label">Total Slippage</div>
                    <div class="metric-change" id="total-slippage-change">--</div>
                </div>
                <div class="summary-metric negative">
                    <div class="metric-value negative" id="execution-slippage">Loading...</div>
                    <div class="metric-label">Execution Slippage</div>
                    <div class="metric-change" id="execution-slippage-change">--</div>
                </div>
                <div class="summary-metric neutral">
                    <div class="metric-value neutral" id="avg-execution-time">Loading...</div>
                    <div class="metric-label">Avg Execution Time</div>
                    <div class="metric-change" id="avg-execution-time-change">--</div>
                </div>
                <div class="summary-metric neutral">
                    <div class="metric-value neutral" id="total-turnover">Loading...</div>
                    <div class="metric-label">Total Turnover</div>
                    <div class="metric-change" id="total-turnover-change">--</div>
                </div>
                <div class="summary-metric neutral">
                    <div class="metric-value neutral" id="total-trades">Loading...</div>
                    <div class="metric-label">Total Trades</div>
                    <div class="metric-change" id="total-trades-change">--</div>
                </div>
                <div class="summary-metric neutral">
                    <div class="metric-value neutral" id="slip-to-turnover-ratio">Loading...</div>
                    <div class="metric-label">Slip-to-Turnover Ratio</div>
                    <div class="metric-change" id="slip-to-turnover-change">--</div>
                </div>
            </div>
        </div>

        <!-- Tab Navigation -->
        <ul class="nav nav-tabs" id="mainTabs" role="tablist">
            <li class="nav-item" role="presentation">
                <button class="nav-link active" id="slippage-tab" data-target="slippage-panel" type="button" role="tab">
                    <i class="fa fa-bar-chart"></i> Slippage Monitoring
                </button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link" id="comparison-tab" data-target="comparison-panel" type="button" role="tab">
                    <i class="fa fa-exchange"></i> Trade Vision
                </button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link" id="analytics-tab" data-target="analytics-panel" type="button" role="tab">
                    <i class="fa fa-dashboard"></i> Combined Analytics
                </button>
            </li>
        </ul>

        <!-- Tab Content -->
        <div class="tab-content" id="mainTabContent">
            <!-- Slippage Monitoring Panel -->
            <div class="tab-pane show active" id="slippage-panel" role="tabpanel">
                <!-- Filter Panel -->
                <div class="filter-panel">
                    <h5><i class="fa fa-filter"></i> Analysis Filters</h5>

                    <!-- First Row: Date Range, Segment, Strategy/Cluster -->
                    <div class="filter-row">
                        <div class="filter-group date-range">
                            <label class="form-label">Date Range</label>
                            <div class="d-flex gap-2">
                                <input type="date" class="form-control" id="start-date" value="2024-01-01">
                                <input type="date" class="form-control" id="end-date" value="2024-01-31">
                            </div>
                        </div>

                        <div class="filter-group">
                            <label class="form-label">Segment</label>
                            <select class="form-select" id="segment-select">
                                <option value="">All Segments</option>
                                <option value="OPTIDX" selected>OPTIDX</option>
                                <option value="OPTIDX_BSE">OPTIDX_BSE</option>
                                <option value="OPTIDX_US">OPTIDX_US</option>
                                <option value="OPTSTK">OPTSTK</option>
                                <option value="FUTSTK">FUTSTK</option>
                            </select>
                        </div>

                        <div class="filter-group">
                            <label class="form-label">Exchange</label>
                            <select class="form-select" id="exchange-select">
                                <option value="">All Exchanges</option>
                                <option value="IND" selected>IND (India Combined)</option>
                                <option value="NSE">NSE</option>
                                <option value="BSE">BSE</option>
                                <option value="US">US</option>
                            </select>
                        </div>
                    </div>

                    <!-- Second Row: Quick Dates, Strategy/Slave Selection, Actions -->
                    <div class="filter-row">
                        <div class="filter-group">
                            <label class="form-label">Quick Dates</label>
                            <div class="quick-dates">
                                <button class="quick-date-btn" data-days="7">7d</button>
                                <button class="quick-date-btn" data-days="30">30d</button>
                                <button class="quick-date-btn" data-days="90">90d</button>
                                <button class="quick-date-btn" data-days="365">1y</button>
                            </div>
                        </div>

                        <div class="filter-group">
                            <label class="form-label">Strategy/Slave</label>
                            <select class="form-select" id="strategy-slave-select">
                                <option value="">All Strategies/Slaves</option>
                            </select>
                        </div>

                        <div class="filter-group actions">
                            <label class="form-label">&nbsp;</label>
                            <div class="d-flex gap-2">
                                <button type="button" class="btn btn-secondary" id="load-filters-btn">
                                    <i class="fa fa-refresh"></i> Load Options
                                </button>
                                <button type="button" class="btn btn-primary" id="run-analysis-btn">
                                    <i class="fa fa-play"></i> Run Analysis
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Charts Section -->
                <div class="content-card">
                    <h4><i class="fa fa-line-chart"></i> Slippage Analysis Charts</h4>

                    <!-- Main Slippage Trend Chart -->
                    <div class="chart-container large">
                        <div class="chart-header">
                            <h5 class="chart-title">Slippage Trend Over Time</h5>
                        </div>
                        <div id="slippage-trend-chart">
                            <div class="loading">
                                <div class="spinner"></div>
                                <span>Loading chart data...</span>
                            </div>
                        </div>
                    </div>

                    <!-- Strategy Comparison Chart -->
                    <div class="chart-container">
                        <div class="chart-header">
                            <h5 class="chart-title">Strategy/Slave Comparison</h5>
                        </div>
                        <div id="strategy-comparison-chart">
                            <div class="loading">
                                <div class="spinner"></div>
                                <span>Loading chart data...</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Trade Vision Panel -->
            <div class="tab-pane fade" id="comparison-panel" role="tabpanel">
                <div class="content-card">
                    <h4><i class="fa fa-exchange"></i> Trade Vision - Strategy Comparison</h4>
                    <p class="text-muted">Compare strategy performance against cluster averages and backtest results</p>

                    <!-- Trade Vision Filter Panel -->
                    <div class="filter-panel">
                        <h5><i class="fa fa-filter"></i> Comparison Filters</h5>

                        <div class="filter-row">
                            <div class="filter-group date-range">
                                <label class="form-label">Date Range</label>
                                <div class="d-flex gap-2">
                                    <input type="date" class="form-control" id="tv-start-date" value="2024-01-01">
                                    <input type="date" class="form-control" id="tv-end-date" value="2024-01-31">
                                </div>
                            </div>

                            <div class="filter-group">
                                <label class="form-label">Strategy/Slave</label>
                                <select class="form-select" id="tv-strategy-select">
                                    <option value="">Select strategy/slave...</option>
                                </select>
                            </div>

                            <div class="filter-group">
                                <label class="form-label">Segment</label>
                                <select class="form-select" id="tv-segment-select">
                                    <option value="OPTIDX" selected>OPTIDX</option>
                                    <option value="OPTIDX_BSE">OPTIDX_BSE</option>
                                    <option value="OPTIDX_US">OPTIDX_US</option>
                                    <option value="OPTSTK">OPTSTK</option>
                                    <option value="FUTSTK">FUTSTK</option>
                                </select>
                            </div>

                            <div class="filter-group actions">
                                <label class="form-label">&nbsp;</label>
                                <button type="button" class="btn btn-primary" id="tv-run-comparison-btn">
                                    <i class="fa fa-play"></i> Run Comparison
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- Trade Vision Results -->
                    <div id="tv-results" style="display: none;">
                        <div class="chart-container">
                            <div class="chart-header">
                                <h5 class="chart-title">Performance Comparison</h5>
                            </div>
                            <div id="tv-comparison-chart">
                                <div class="loading">
                                    <div class="spinner"></div>
                                    <span>Loading comparison data...</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Combined Analytics Panel -->
            <div class="tab-pane fade" id="analytics-panel" role="tabpanel">
                <div class="content-card">
                    <h4><i class="fa fa-dashboard"></i> Combined Analytics</h4>
                    <p class="text-muted">Integrated view combining slippage analysis with trade comparison metrics</p>

                    <!-- Coming Soon Placeholder -->
                    <div class="text-center py-5">
                        <i class="fa fa-cogs fa-3x text-muted mb-3"></i>
                        <h5 class="text-muted">Advanced Analytics Coming Soon</h5>
                        <p class="text-muted">This section will provide integrated analysis combining both slippage monitoring and trade comparison features.</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</main>

<script>
// Advanced Slippage Analysis JavaScript
document.addEventListener('DOMContentLoaded', function() {
    console.log('Advanced Slippage Analysis initialized');

    // Initialize components
    initializeFilters();
    initializeTabs();
    updateTimestamp();

    // Load initial data
    loadFilterOptions();
});

// Tab functionality
function initializeTabs() {
    document.querySelectorAll('#mainTabs .nav-link').forEach(tab => {
        tab.addEventListener('click', function() {
            const targetId = this.dataset.target;

            // Update tab states
            document.querySelectorAll('#mainTabs .nav-link').forEach(t => t.classList.remove('active'));
            document.querySelectorAll('.tab-pane').forEach(p => {
                p.classList.remove('show', 'active');
            });

            this.classList.add('active');
            document.getElementById(targetId).classList.add('show', 'active');
        });
    });
}

// Filter functionality
function initializeFilters() {
    // Quick date buttons
    document.querySelectorAll('.quick-date-btn').forEach(btn => {
        btn.addEventListener('click', function() {
            const days = parseInt(this.dataset.days);
            const endDate = new Date();
            const startDate = new Date();
            startDate.setDate(endDate.getDate() - days);

            document.getElementById('start-date').value = startDate.toISOString().split('T')[0];
            document.getElementById('end-date').value = endDate.toISOString().split('T')[0];
        });
    });

    // Load filters button
    document.getElementById('load-filters-btn')?.addEventListener('click', loadFilterOptions);

    // Run analysis button
    document.getElementById('run-analysis-btn')?.addEventListener('click', runSlippageAnalysis);

    // Trade Vision run comparison button
    document.getElementById('tv-run-comparison-btn')?.addEventListener('click', runTradeVisionComparison);
}

// Load filter options from backend
async function loadFilterOptions() {
    try {
        showLoading('Loading filter options...');

        const response = await fetch('/slip_dashboard/api/filter_options');
        const data = await response.json();

        if (data.success) {
            populateFilterOptions(data.data);
            showNotification('Filter options loaded successfully', 'success');
        } else {
            throw new Error(data.error || 'Failed to load filter options');
        }
    } catch (error) {
        console.error('Error loading filter options:', error);
        showNotification('Failed to load filter options: ' + error.message, 'error');
    } finally {
        hideLoading();
    }
}

// Populate filter dropdowns
function populateFilterOptions(data) {
    // Populate strategy/slave dropdown
    const strategySelect = document.getElementById('strategy-slave-select');
    const tvStrategySelect = document.getElementById('tv-strategy-select');

    if (strategySelect && data.strategies) {
        strategySelect.innerHTML = '<option value="">All Strategies/Slaves</option>';
        data.strategies.forEach(strategy => {
            strategySelect.innerHTML += `<option value="${strategy}">${strategy}</option>`;
        });
    }

    if (tvStrategySelect && data.strategies) {
        tvStrategySelect.innerHTML = '<option value="">Select strategy/slave...</option>';
        data.strategies.forEach(strategy => {
            tvStrategySelect.innerHTML += `<option value="${strategy}">${strategy}</option>`;
        });
    }
}

// Run slippage analysis
async function runSlippageAnalysis() {
    try {
        showLoading('Running slippage analysis...');

        const filters = {
            start_date: document.getElementById('start-date').value,
            end_date: document.getElementById('end-date').value,
            segments: [document.getElementById('segment-select').value].filter(Boolean),
            exchanges: [document.getElementById('exchange-select').value].filter(Boolean),
            strategies: [document.getElementById('strategy-slave-select').value].filter(Boolean)
        };

        const response = await fetch('/slip_dashboard/api/slippage_data', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(filters)
        });

        const data = await response.json();

        if (data.success) {
            updateSummaryMetrics(data.summary_stats);
            createSlippageCharts(data.charts);
            showNotification('Analysis completed successfully', 'success');
        } else {
            throw new Error(data.error || 'Analysis failed');
        }
    } catch (error) {
        console.error('Error running analysis:', error);
        showNotification('Analysis failed: ' + error.message, 'error');
    } finally {
        hideLoading();
    }
}

// Run Trade Vision comparison
async function runTradeVisionComparison() {
    try {
        const strategySelect = document.getElementById('tv-strategy-select');
        if (!strategySelect.value) {
            showNotification('Please select a strategy/slave for comparison', 'warning');
            return;
        }

        showLoading('Running trade comparison...');

        const filters = {
            start_date: document.getElementById('tv-start-date').value,
            end_date: document.getElementById('tv-end-date').value,
            slave_strategy: strategySelect.value,
            segment: document.getElementById('tv-segment-select').value,
            exchange: 'IND'
        };

        const response = await fetch('/slip_dashboard/api/trade_comparison', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(filters)
        });

        const data = await response.json();

        if (data.success) {
            document.getElementById('tv-results').style.display = 'block';
            createTradeVisionChart(data.data);
            showNotification('Comparison completed successfully', 'success');
        } else {
            throw new Error(data.error || 'Comparison failed');
        }
    } catch (error) {
        console.error('Error running comparison:', error);
        showNotification('Comparison failed: ' + error.message, 'error');
    } finally {
        hideLoading();
    }
}

// Update summary metrics
function updateSummaryMetrics(stats) {
    if (!stats) return;

    document.getElementById('total-slippage').textContent = formatCurrency(stats.total_slippage || 0);
    document.getElementById('execution-slippage').textContent = formatCurrency(stats.execution_slippage || 0);
    document.getElementById('avg-execution-time').textContent = formatTime(stats.avg_execution_time || 0);
    document.getElementById('total-turnover').textContent = formatCurrency(stats.total_turnover || 0);
    document.getElementById('total-trades').textContent = formatNumber(stats.total_trades || 0);
    document.getElementById('slip-to-turnover-ratio').textContent = formatBPS(stats.slip_to_turnover_bps || 0);
}

// Create slippage charts
function createSlippageCharts(charts) {
    if (charts.slippage_trend) {
        createChart('slippage-trend-chart', charts.slippage_trend);
    }
    if (charts.strategy_comparison) {
        createChart('strategy-comparison-chart', charts.strategy_comparison);
    }
}

// Create Trade Vision chart
function createTradeVisionChart(data) {
    if (data.charts && data.charts.performance_comparison) {
        createChart('tv-comparison-chart', data.charts.performance_comparison);
    }
}

// Generic chart creation function
function createChart(containerId, chartData) {
    const container = document.getElementById(containerId);
    if (!container) return;

    // Remove loading indicator
    container.innerHTML = '';

    // Create Plotly chart
    Plotly.newPlot(containerId, chartData.data, chartData.layout, {
        responsive: true,
        displayModeBar: true,
        modeBarButtonsToRemove: ['pan2d', 'lasso2d', 'select2d'],
        displaylogo: false
    });
}

// Utility functions
function formatCurrency(value) {
    if (Math.abs(value) >= 1e7) {
        return '₹' + (value / 1e7).toFixed(1) + 'Cr';
    } else if (Math.abs(value) >= 1e5) {
        return '₹' + (value / 1e5).toFixed(1) + 'L';
    } else if (Math.abs(value) >= 1e3) {
        return '₹' + (value / 1e3).toFixed(1) + 'K';
    } else {
        return '₹' + value.toFixed(0);
    }
}

function formatTime(seconds) {
    if (seconds >= 60) {
        return (seconds / 60).toFixed(1) + 'm';
    } else {
        return seconds.toFixed(2) + 's';
    }
}

function formatNumber(value) {
    return value.toLocaleString();
}

function formatBPS(value) {
    return value.toFixed(1) + ' BPS';
}

function updateTimestamp() {
    const now = new Date();
    const timestamp = now.toLocaleString('en-IN', {
        year: 'numeric',
        month: 'short',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit'
    });
    document.getElementById('last-update-time').textContent = timestamp;
}

// Loading and notification functions
function showLoading(message = 'Loading...') {
    // You can implement a global loading indicator here
    console.log('Loading:', message);
}

function hideLoading() {
    console.log('Loading complete');
}

function showNotification(message, type = 'info') {
    // You can implement a notification system here
    console.log(`${type.toUpperCase()}: ${message}`);

    // Simple alert for now
    if (type === 'error') {
        alert('Error: ' + message);
    }
}
</script>

{% endblock content %}
