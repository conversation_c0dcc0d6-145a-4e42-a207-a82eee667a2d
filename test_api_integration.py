#!/usr/bin/env python3
"""
Test script to verify API integration for the advanced slippage dashboard.
"""

import requests
import json
import sys

# Base URL for the Flask application
BASE_URL = "http://127.0.0.1:9005"

def test_filter_options():
    """Test the filter options API endpoint."""
    print("Testing filter options endpoint...")
    
    try:
        response = requests.get(f"{BASE_URL}/slip_dashboard/api/filter_options")
        
        if response.status_code == 200:
            data = response.json()
            print("✅ Filter options endpoint working")
            print(f"   Response structure: {list(data.keys())}")
            
            if 'success' in data and data['success']:
                print("✅ Success flag is True")
                
                if 'data' in data:
                    print("✅ Data field present")
                    data_keys = list(data['data'].keys())
                    print(f"   Data keys: {data_keys}")
                    
                    # Check for expected keys
                    expected_keys = ['segments', 'strategies', 'exchanges']
                    for key in expected_keys:
                        if key in data_keys:
                            print(f"✅ {key} field present")
                        else:
                            print(f"❌ {key} field missing")
                else:
                    print("❌ Data field missing")
            else:
                print("❌ Success flag is False or missing")
        else:
            print(f"❌ Filter options endpoint failed with status {response.status_code}")
            print(f"   Response: {response.text}")
            
    except Exception as e:
        print(f"❌ Error testing filter options: {e}")

def test_slippage_data():
    """Test the slippage data API endpoint."""
    print("\nTesting slippage data endpoint...")
    
    try:
        # Test payload
        payload = {
            "start_date": "2024-01-01",
            "end_date": "2024-01-31",
            "segments": ["OPTIDX"],
            "exchanges": ["IND"],
            "strategies": []
        }
        
        response = requests.post(
            f"{BASE_URL}/slip_dashboard/api/slippage_data",
            json=payload,
            headers={'Content-Type': 'application/json'}
        )
        
        if response.status_code == 200:
            data = response.json()
            print("✅ Slippage data endpoint working")
            print(f"   Response structure: {list(data.keys())}")
            
            if 'success' in data and data['success']:
                print("✅ Success flag is True")
                
                # Check for expected fields
                expected_fields = ['summary_stats', 'charts', 'raw_data']
                for field in expected_fields:
                    if field in data:
                        print(f"✅ {field} field present")
                        if field == 'summary_stats' and isinstance(data[field], dict):
                            stats_keys = list(data[field].keys())
                            print(f"   Summary stats keys: {stats_keys}")
                    else:
                        print(f"❌ {field} field missing")
            else:
                print("❌ Success flag is False or missing")
                if 'error' in data:
                    print(f"   Error: {data['error']}")
        else:
            print(f"❌ Slippage data endpoint failed with status {response.status_code}")
            print(f"   Response: {response.text}")
            
    except Exception as e:
        print(f"❌ Error testing slippage data: {e}")

def test_trade_comparison():
    """Test the trade comparison API endpoint."""
    print("\nTesting trade comparison endpoint...")
    
    try:
        # Test payload
        payload = {
            "start_date": "2024-01-01",
            "end_date": "2024-01-31",
            "slave_strategy": "test_strategy",
            "segment": "OPTIDX",
            "exchange": "IND"
        }
        
        response = requests.post(
            f"{BASE_URL}/slip_dashboard/api/trade_comparison",
            json=payload,
            headers={'Content-Type': 'application/json'}
        )
        
        if response.status_code == 200:
            data = response.json()
            print("✅ Trade comparison endpoint working")
            print(f"   Response structure: {list(data.keys())}")
            
            if 'success' in data and data['success']:
                print("✅ Success flag is True")
            else:
                print("❌ Success flag is False or missing")
                if 'error' in data:
                    print(f"   Error: {data['error']}")
        else:
            print(f"❌ Trade comparison endpoint failed with status {response.status_code}")
            print(f"   Response: {response.text}")
            
    except Exception as e:
        print(f"❌ Error testing trade comparison: {e}")

def main():
    """Run all API tests."""
    print("🧪 Testing Advanced Slippage Dashboard API Integration")
    print("=" * 60)
    
    # Test all endpoints
    test_filter_options()
    test_slippage_data()
    test_trade_comparison()
    
    print("\n" + "=" * 60)
    print("✅ API integration testing completed!")

if __name__ == "__main__":
    main()
